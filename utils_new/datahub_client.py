# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/19 
@Auth ： AI Assistant
@File ：datahub_client.py
@IDE ：PyCharm
@Motto：DataHub工具类，用于数据集管理操作
"""
import logging
from typing import List, Optional

from datahub.emitter.mce_builder import make_dataset_urn
from datahub.ingestion.graph.client import DatahubClientConfig, DataHubGraph
import datahub.emitter.mce_builder as builder
from datahub.emitter.rest_emitter import DatahubRestEmitter
from datahub.metadata.schema_classes import UpstreamLineageClass

from datahub_dev.config import api_token


# 设置日志
log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# 全局配置常量
DEFAULT_SERVER_URL = "http://120.27.143.32:8080"
DEFAULT_API_TOKEN = "eyJhbGciOiJIUzI1NiJ9.eyJhY3RvclR5cGUiOiJVU0VSIiwiYWN0b3JJZCI6IkNoYXVuY2V5LkxpIiwidHlwZSI6IlBFUlNPTkFMIiwidmVyc2lvbiI6IjIiLCJqdGkiOiI2NTg2MmU1YS03NzIyLTQwNjMtOWJiYi05NGUxODZhYjFmZDUiLCJzdWIiOiJDaGF1bmNleS5MaSIsImlzcyI6ImRhdGFodWItbWV0YWRhdGEtc2VydmljZSJ9.NRTc2hjBtm9ccGf1_0sTyXknX9vDw7c-Guhb5nwHIaQ"
DEFAULT_PLATFORM = "hive"


class DataHubClient:
    """
    DataHub客户端工具类，提供数据集管理功能
    """
    
    def __init__(self, server_url: str = DEFAULT_SERVER_URL, api_token: Optional[str] = DEFAULT_API_TOKEN):
        """
        初始化DataHub客户端
        
        Args:
            server_url: DataHub服务器地址
            api_token: API认证令牌
        """
        self.server_url = server_url
        self.api_token = api_token
        self._graph = None
    
    @property
    def graph(self) -> DataHubGraph:
        """
        获取DataHubGraph实例（懒加载）
        """
        if self._graph is None:
            config = DatahubClientConfig(
                server=self.server_url,
                token=self.api_token
            )
            self._graph = DataHubGraph(config=config)
        return self._graph
    
    def delete_dataset(self, dataset_name: str, platform: str = "hive", hard: bool = False) -> bool:
        """
        删除单个数据集
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            hard: 是否硬删除，默认为False（软删除）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 创建数据集URN
            dataset_urn = make_dataset_urn(name=dataset_name, platform=platform)
            
            # 删除数据集
            self.graph.delete_entity(urn=dataset_urn, hard=hard)
            
            delete_type = "硬删除" if hard else "软删除"
            log.info(f"{delete_type}数据集成功: {dataset_urn}")
            return True
            
        except Exception as e:
            log.error(f"删除数据集失败 {dataset_name}: {str(e)}")
            return False
    
    def soft_delete_dataset(self, dataset_name: str, platform: str = "hive") -> bool:
        """
        软删除数据集（兼容性方法）
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            
        Returns:
            bool: 删除是否成功
        """
        return self.delete_dataset(dataset_name, platform, hard=False)
    
    def hard_delete_dataset(self, dataset_name: str, platform: str = "hive") -> bool:
        """
        硬删除数据集
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            
        Returns:
            bool: 删除是否成功
        """
        return self.delete_dataset(dataset_name, platform, hard=True)

    def create_lineage(self, upstream_datasets: List[str], downstream_dataset: str,
                      upstream_platform: str = DEFAULT_PLATFORM, downstream_platform: str = DEFAULT_PLATFORM) -> bool:
        """
        创建数据血缘关系

        Args:
            upstream_datasets: 上游数据集名称列表
            downstream_dataset: 下游数据集名称
            upstream_platform: 上游数据平台名称，默认为"hive"
            downstream_platform: 下游数据平台名称，默认为"hive"

        Returns:
            bool: 创建是否成功
        """
        try:
            # 构建上游数据集URN列表
            upstream_urns = [
                builder.make_dataset_urn(upstream_platform, dataset_name)
                for dataset_name in upstream_datasets
            ]

            # 构建下游数据集URN
            downstream_urn = builder.make_dataset_urn(downstream_platform, downstream_dataset)

            # 构建血缘关系MCE
            lineage_mce = builder.make_lineage_mce(upstream_urns, downstream_urn)

            # 创建REST发射器
            emitter = DatahubRestEmitter(self.server_url, self.api_token)

            # 发射血缘元数据
            emitter.emit_mce(lineage_mce)

            log.info(f"成功创建血缘关系: {upstream_datasets}({upstream_platform}) -> {downstream_dataset}({downstream_platform})")
            return True

        except Exception as e:
            log.error(f"创建血缘关系失败: {str(e)}")
            return False

    def create_simple_lineage(self, upstream_dataset: str, downstream_dataset: str,
                             upstream_platform: str = DEFAULT_PLATFORM, downstream_platform: str = DEFAULT_PLATFORM) -> bool:
        """
        创建简单的一对一数据血缘关系

        Args:
            upstream_dataset: 上游数据集名称
            downstream_dataset: 下游数据集名称
            upstream_platform: 上游数据平台名称，默认为"hive"
            downstream_platform: 下游数据平台名称，默认为"hive"

        Returns:
            bool: 创建是否成功
        """
        return self.create_lineage([upstream_dataset], downstream_dataset, upstream_platform, downstream_platform)

    def create_incremental_lineage(self, upstream_datasets: List[str], downstream_dataset: str,
                                   upstream_platform: str = DEFAULT_PLATFORM,
                                   downstream_platform: str = DEFAULT_PLATFORM) -> bool:
        """
        增量创建数据血缘关系。
        它会获取现有的血缘，合并新的上游，然后一次性写回。

        Args:
            upstream_datasets: 需要添加的上游数据集名称列表
            downstream_dataset: 下游数据集名称
            upstream_platform: 上游数据平台名称
            downstream_platform: 下游数据平台名称

        Returns:
            bool: 创建是否成功
        """
        try:
            downstream_urn = builder.make_dataset_urn(downstream_platform, downstream_dataset)

            # 获取现有的上游血缘
            existing_upstream_urns = set()
            try:
                existing_lineage: Optional[UpstreamLineageClass] = self.graph.get_aspect(
                    entity_urn=downstream_urn,
                    aspect_type=UpstreamLineageClass,
                )
                if existing_lineage and existing_lineage.upstreams:
                    existing_upstream_urns = {
                        upstream.dataset for upstream in existing_lineage.upstreams
                    }
            except Exception as e:
                log.warning(f"无法获取现有的血缘关系 '{downstream_urn}': {e}。将创建新的血缘。")

            # 创建新的上游URN并与现有的合并
            new_upstream_urns = {
                builder.make_dataset_urn(upstream_platform, dataset_name)
                for dataset_name in upstream_datasets
            }
            all_upstream_urns = list(existing_upstream_urns.union(new_upstream_urns))

            if not all_upstream_urns:
                log.warning("没有指定上游数据集，不执行任何操作。")
                return True

            # 构建并发送血缘关系MCE
            lineage_mce = builder.make_lineage_mce(all_upstream_urns, downstream_urn)
            emitter = DatahubRestEmitter(self.server_url, self.api_token)
            emitter.emit_mce(lineage_mce)

            log.info(f"成功增量更新血缘关系: {len(new_upstream_urns)}个新上游 -> {downstream_dataset}")
            return True

        except Exception as e:
            log.error(f"增量创建血缘关系失败: {str(e)}")
            return False


def create_datahub_client_from_config(config_path: str = "./datahub_dev/config.json") -> DataHubClient:
    """
    从配置文件创建DataHub客户端
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        DataHubClient: 配置好的客户端实例
    """
    try:
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        api_token = config.get('api_token', DEFAULT_API_TOKEN)
        server_url = config.get('server_url', DEFAULT_SERVER_URL)
        
        return DataHubClient(server_url=server_url, api_token=api_token)
        
    except Exception as e:
        log.warning(f"从配置文件加载失败: {str(e)}, 使用默认配置")
        return DataHubClient()


# 便捷函数
def quick_delete_dataset(dataset_name: str, platform: str = DEFAULT_PLATFORM, hard: bool = False,
                        server_url: str = DEFAULT_SERVER_URL, api_token: Optional[str] = DEFAULT_API_TOKEN) -> bool:
    """
    快速删除单个数据集的便捷函数
    
    Args:
        dataset_name: 数据集名称
        platform: 数据平台名称
        hard: 是否硬删除
        server_url: DataHub服务器地址
        api_token: API认证令牌
        
    Returns:
        bool: 删除是否成功
    """
    client = DataHubClient(server_url=server_url, api_token=api_token)
    return client.delete_dataset(dataset_name, platform, hard)


def create_quick_incremental_lineage(upstream_datasets: List[str], downstream_dataset: str,
                                     upstream_platform: str = DEFAULT_PLATFORM,
                                     downstream_platform: str = DEFAULT_PLATFORM,
                                     server_url: str = DEFAULT_SERVER_URL,
                                     api_token: Optional[str] = DEFAULT_API_TOKEN) -> bool:
    """
    快速增量创建数据血缘关系的便捷函数

    Args:
        upstream_datasets: 上游数据集名称列表
        downstream_dataset: 下游数据集名称
        upstream_platform: 上游数据平台名称
        downstream_platform: 下游数据平台名称
        server_url: DataHub服务器地址
        api_token: API认证令牌

    Returns:
        bool: 创建是否成功
    """
    client = DataHubClient(server_url=server_url, api_token=api_token)
    return client.create_incremental_lineage(
        upstream_datasets, downstream_dataset, upstream_platform, downstream_platform
    )


def create_quick_lineage(upstream_dataset: str, downstream_dataset: str, 
                        upstream_platform: str = DEFAULT_PLATFORM, 
                        downstream_platform: str = DEFAULT_PLATFORM,
                        server_url: str = DEFAULT_SERVER_URL) -> bool:
    """
    快速创建数据血缘关系的便捷函数
    
    Args:
        upstream_dataset: 上游数据集名称
        downstream_dataset: 下游数据集名称
        upstream_platform: 上游数据平台名称，默认为"hive"
        downstream_platform: 下游数据平台名称，默认为"hive"
        server_url: DataHub服务器地址
        
    Returns:
        bool: 创建是否成功
    """
    try:
        # Construct a lineage object.
        lineage_mce = builder.make_lineage_mce(
            [
                builder.make_dataset_urn(upstream_platform, upstream_dataset),  # Upstream
            ],
            builder.make_dataset_urn(downstream_platform, downstream_dataset),  # Downstream
        )

        # Create an emitter to the GMS REST API.
        emitter = DatahubRestEmitter(server_url, api_token)

        # Emit metadata!
        emitter.emit_mce(lineage_mce)
        
        log.info(f"快速创建血缘关系成功: {upstream_dataset}({upstream_platform}) -> {downstream_dataset}({downstream_platform})")
        return True
        
    except Exception as e:
        log.error(f"快速创建血缘关系失败: {str(e)}")
        return False