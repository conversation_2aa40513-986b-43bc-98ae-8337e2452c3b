# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/19 
@Auth ： AI Assistant
@File ：datahub_example.py
@IDE ：PyCharm
@Motto：DataHub工具类使用示例
"""
from datahub_dev.config import api_token
from utils_new.datahub_client import DataHubClient, create_datahub_client_from_config, quick_delete_dataset,create_quick_lineage, create_quick_incremental_lineage


if __name__ == "__main__":
    create_quick_incremental_lineage(
        upstream_datasets=['004b1e08-b35e-4265-98ed-1508a2cbd4e8'],
        downstream_dataset='0012539b-eb89-46bc-8f88-bfcc6398c706',
        upstream_platform="FineDataLink",
        downstream_platform="FineDataLink"
    )
    create_quick_incremental_lineage(
        upstream_datasets=['005ad12d-c7b7-40d7-837d-d401ea5da088'],
        downstream_dataset='0012539b-eb89-46bc-8f88-bfcc6398c706',
        upstream_platform="FineDataLink",
        downstream_platform="FineDataLink"
    )
