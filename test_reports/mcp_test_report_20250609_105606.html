
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 測試報告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { font-size: 24px; margin: 10px 0; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .card h3 { margin: 0 0 10px 0; color: #333; }
        .card .value { font-size: 24px; font-weight: bold; color: #007bff; }
        .scenarios { margin: 20px 0; }
        .scenario { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745; }
        .scenario.failed { border-left-color: #dc3545; }
        .scenario h4 { margin: 0 0 10px 0; }
        .scenario-details { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px; }
        .errors { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .performance { margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 MCP 測試報告</h1>
            <div class="status warning">
                ⚠️ 測試完成
            </div>
            <p>生成時間: 2025-06-09T10:56:06.569058</p>
        </div>
        
        <div class="summary">
            <div class="card">
                <h3>總測試數</h3>
                <div class="value">2</div>
            </div>
            <div class="card">
                <h3>通過測試</h3>
                <div class="value" style="color: #28a745;">1</div>
            </div>
            <div class="card">
                <h3>失敗測試</h3>
                <div class="value" style="color: #dc3545;">1</div>
            </div>
            <div class="card">
                <h3>成功率</h3>
                <div class="value">50.0%</div>
            </div>
            <div class="card">
                <h3>總耗時</h3>
                <div class="value">500.0ms</div>
            </div>
            <div class="card">
                <h3>平均耗時</h3>
                <div class="value">500.0ms</div>
            </div>
        </div>
        
        <div class="scenarios">
            <h2>📋 測試場景詳情</h2>

            <div class="scenario ">
                <h4>✅ mock_test</h4>
                <p>模擬測試場景，用於演示測試框架功能</p>
                <div class="scenario-details">
                    <div><strong>狀態:</strong> 通過</div>
                    <div><strong>耗時:</strong> 500.0ms</div>
                    <div><strong>完成步驟:</strong> 3/3</div>
                    <div><strong>錯誤數:</strong> 0</div>
                </div>
</div>
            <div class="scenario failed">
                <h4>❌ quick_connection</h4>
                <p>測試 MCP 服務器的快速啟動和連接</p>
                <div class="scenario-details">
                    <div><strong>狀態:</strong> 失敗</div>
                    <div><strong>耗時:</strong> N/A</div>
                    <div><strong>完成步驟:</strong> 0/0</div>
                    <div><strong>錯誤數:</strong> 1</div>
                </div>
<div class="errors"><strong>錯誤信息:</strong><ul><li>服務器啟動失敗</li></ul></div></div>
        </div>
        
        <div class="performance">
            <h2>📊 性能統計</h2>
            <div class="summary">
                <div class="card">
                    <h3>最快測試</h3>
                    <div class="value">500.0ms</div>
                </div>
                <div class="card">
                    <h3>最慢測試</h3>
                    <div class="value">500.0ms</div>
                </div>
                <div class="card">
                    <h3>中位數</h3>
                    <div class="value">500.0ms</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>MCP Feedback Enhanced 測試框架 | 生成時間: 2025-06-09 10:56:06</p>
        </div>
    </div>
</body>
</html>
