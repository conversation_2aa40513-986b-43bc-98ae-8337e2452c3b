<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字动画测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .number {
            font-size: 2rem;
            font-weight: bold;
            color: #007aff;
            margin: 10px 0;
        }
        .label {
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>数字动画效果测试</h1>
    
    <div class="test-container">
        <div class="label">数据资产分类数量测试：</div>
        <div class="number" id="test1">0</div>
    </div>
    
    <div class="test-container">
        <div class="label">域统计数据测试：</div>
        <div class="number" id="test2">0</div>
        <div class="number" id="test3">0</div>
        <div class="number" id="test4">0</div>
    </div>
    
    <div class="test-container">
        <div class="label">大数字测试：</div>
        <div class="number" id="test5">0</div>
    </div>

    <script>
        // 复制动画函数
        function animateNumber(element, target, duration = 4000, delay = 0) {
            const targetNum = typeof target === 'string' ? parseInt(target.replace(/,/g, '')) : target;
            
            setTimeout(() => {
                let current = 0;
                const startTime = Date.now();
                const endTime = startTime + duration;
                
                const updateNumber = () => {
                    const now = Date.now();
                    const progress = Math.min((now - startTime) / duration, 1);
                    
                    // 使用缓动函数让动画更自然
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    current = targetNum * easeOutQuart;
                    
                    // 格式化数字显示
                    if (targetNum >= 1000) {
                        element.textContent = Math.floor(current).toLocaleString();
                    } else {
                        element.textContent = Math.floor(current);
                    }
                    
                    if (progress < 1) {
                        requestAnimationFrame(updateNumber);
                    } else {
                        // 确保最终显示准确的目标值
                        if (targetNum >= 1000) {
                            element.textContent = targetNum.toLocaleString();
                        } else {
                            element.textContent = targetNum;
                        }
                    }
                };
                
                requestAnimationFrame(updateNumber);
            }, delay);
        }

        // 测试动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateNumber(document.getElementById('test1'), 1400, 4000, 0);
                animateNumber(document.getElementById('test2'), 98, 4000, 200);
                animateNumber(document.getElementById('test3'), 15, 4000, 400);
                animateNumber(document.getElementById('test4'), 28, 4000, 600);
                animateNumber(document.getElementById('test5'), 7000, 4000, 800);
            }, 500);
        });
    </script>
</body>
</html>
