import requests
import json
import warnings

from datahub_dev.config import api_token

# 忽略 InsecureRequestWarning


def create_domain(name: str, parent_domain_urn: str):
    """
    根据提供的 curl 命令在 DataHub 中创建数据域。

    Args:
        name (str): 新数据域的名称。
        parent_domain_urn (str): 父数据域的 URN。

    Returns:
        dict: API 返回的 JSON 响应，如果请求失败则返回 None。
    """
    url = 'http://120.27.143.32:9003/api/v2/graphql'

    headers = {
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Origin': 'http://120.27.143.32:9003',
        'Proxy-Connection': 'keep-alive',
        'Referer': 'http://120.27.143.32:9003/domains',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'accept': '*/*',
        'content-type': 'application/json',
    }

    # 注意: PLAY_SESSION token 通常有有效期，如果脚本失效，您可能需要更新它。
    cookies = {
        'bid': '13773906-74ed-45c9-b8f2-0e37cacfabd9',
        'PLAY_SESSION':api_token,
        'actor': 'urn:li:corpuser:Chauncey.Li',
    }
    
    graphql_payload = {
        "operationName": "createDomain",
        "variables": {
            "input": {
                "name": name,
                "parentDomain": parent_domain_urn
            }
        },
        "query": "mutation createDomain($input: CreateDomainInput!) {\\n  createDomain(input: $input)\\n}\\n"
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            json=graphql_payload,
            verify=False  # 对应 curl 的 --insecure 标志
        )
        response.raise_for_status()  # 如果请求返回失败的状态码，则抛出异常
        print(f"成功发起创建数据域 '{name}' 的请求。")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"创建数据域 '{name}' 时出错: {e}")
        if e.response is not None:
            print(f"响应状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")
        return None

if __name__ == '__main__':
    # 从 curl 命令中提取的参数，用于调用函数
    domain_name_to_create = "产研开发管理"
    parent_domain_urn_to_use = "urn:li:domain:5476f6ea-d6af-4bc7-8d31-4cf6920f1226"
    
    # 调用函数创建数据域
    result = create_domain(domain_name_to_create, parent_domain_urn_to_use)
    
    if result:
        print("\\nAPI 响应内容:")
        # 使用 json.dumps 美化输出，并确保中文字符正常显示
        print(json.dumps(result, indent=2, ensure_ascii=False))
