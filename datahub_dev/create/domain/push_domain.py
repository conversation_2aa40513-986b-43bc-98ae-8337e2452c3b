"""
# @Time    : 2025/6/7 17:18
# <AUTHOR> <PERSON><PERSON><PERSON>.Li
# @File    : push_domain.py
# @Desc    : 
"""
from datahub_dev.config import api_token
from utils_new.pg_client import Database
from datahub.emitter.mce_builder import make_dataset_urn
from datahub.emitter.mcp import MetadataChangeProposalWrapper
from datahub.emitter.rest_emitter import DatahubRestEmitter
from datahub.metadata.schema_classes import DomainsClass


db = Database()


if __name__ == "__main__":
    results = db.execute_sql("""
                             SELECT CONCAT('fine_data.', table_schema, '.', t1.table_name) AS table_name, doamin
                             FROM (SELECT table_name, SPLIT_PART(path_name, '/', 1) AS doamin
                                   FROM ods_dw_sql_code_df
                                   WHERE SPLIT_PART(path_name, '/', 1) IN ('财务-fin',
                                                                           '简道云特有业务-jdy',
                                                                           '产品与研发-prod',
                                                                           '运营-oprn',
                                                                           '市场-mkt',
                                                                           'IDP',
                                                                           '客户-cust',
                                                                           '公共数据-pub',
                                                                           '产品-prod',
                                                                           '行政-ad',
                                                                           '客户服务与技术支持-ts',
                                                                           '数仓治理-dw',
                                                                           '人事-hr',
                                                                           '销售过程-sales')) t1
                                      JOIN (SELECT table_name, table_schema
                                            FROM information_schema.tables) t2 ON t1.table_name = t2.table_name
                             """)

    domain_data = [
        {"urn": "urn:li:domain:d7900b06-2fe6-4807-9780-690ff0668d78", "name": "销售过程-sales"},
        {"urn": "urn:li:domain:5d46b987-7e63-4896-bd25-e376cba6e632", "name": "人事-hr"},
        {"urn": "urn:li:domain:b20aff38-94a3-4125-8492-e879adb5ff10", "name": "数仓治理-dw"},
        {"urn": "urn:li:domain:21eea34f-4dfe-4c03-85e9-52f24dc91996", "name": "客户服务与技术支持-ts"},
        {"urn": "urn:li:domain:ec81d8db-93f3-4357-a98a-52a1798c1775", "name": "行政-ad"},
        {"urn": "urn:li:domain:cb0d006f-c946-46b5-9d11-2487a8351bde", "name": "公共数据-pub"},
        {"urn": "urn:li:domain:13de0e97-b0eb-42b3-8307-bca2e59b95eb", "name": "客户-cust"},
        {"urn": "urn:li:domain:6b0950be-a29c-49c5-8924-731b76b27400", "name": "IDP"},
        {"urn": "urn:li:domain:2ca9e9ae-ae58-4663-b35b-4e8dc8311edc", "name": "市场-mkt"},
        {"urn": "urn:li:domain:80a4c457-2a9c-4f54-8f26-54aa1322a90d", "name": "运营-oprn"},
        {"urn": "urn:li:domain:d7ceed5b-536a-4dc2-9499-f85164669baf", "name": "产品与研发-prod"},
        {"urn": "urn:li:domain:94894eb3-7a74-4bc2-83cd-7ca11575db38", "name": "简道云特有业务-jdy"},
        {"urn": "urn:li:domain:5476f6ea-d6af-4bc7-8d31-4cf6920f1226", "name": "财务-fin"},
    ]

    domain_urn_map = {item["name"]: item["urn"] for item in domain_data}

    # Configure the DataHub emitter
    gms_endpoint = "http://120.27.143.32:8080"  # Or your DataHub GMS endpoint
    emitter = DatahubRestEmitter(gms_endpoint,api_token)

    if results:
        for row in results:
            dataset_name = row[0]
            domain_name = row[1]

            dataset_urn = make_dataset_urn("postgres", dataset_name, "PROD")

            domain_urn = domain_urn_map.get(domain_name)

            if not domain_urn:
                print(f"Warning: Domain '{domain_name}' not found for table '{dataset_name}'")
                continue

            mcp = MetadataChangeProposalWrapper(
                entityType="dataset",
                entityUrn=dataset_urn,
                aspectName="domains",
                aspect=DomainsClass(domains=[domain_urn]),
            )

            emitter.emit(mcp)
            print(f"Setting domain for dataset {dataset_urn} to {domain_urn}")

        emitter.flush()
        print("Finished setting domains.")
