from datahub_dev.config import api_token

import datahub.emitter.mce_builder as builder
from datahub.emitter.rest_emitter import DatahubRestEmitter
import requests
import json
import time

from utils_new.datahub_client import create_quick_lineage
from utils_new.pg_client import Database

db = Database()


if __name__ == "__main__":
    # 查询FDL任务之间的关系
    results = db.execute_sql("""
                             SELECT task_id, down_task_id
                             FROM (SELECT task_id, down_task_id
                                   FROM public.test_events_task_link

                                   UNION ALL
                                   SELECT task_id, down_task_id
                                   FROM public.test_canvas_task_link) t
                             WHERE task_id IN (SELECT task_id
                                               FROM public.test_fdl_it_task_data_info)
                               AND down_task_id IN (SELECT task_id
                                                    FROM public.test_fdl_it_task_data_info)
                             GROUP BY task_id, down_task_id
                             """)

    if results:
        for row in results:
            task_id = row[0] if row[0] else "NULL"
            down_task_id = row[1] if row[1] else "NULL"
            result = create_quick_lineage(
                upstream_dataset=task_id,
                downstream_dataset=down_task_id,
                upstream_platform="FineDataLink",
                downstream_platform="FineDataLink"
            )
            print(result)
    else:
        print("未查询到任何数据")
