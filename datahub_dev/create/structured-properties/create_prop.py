"""
# @Time    : 2025/6/8 19:58
# <AUTHOR> <PERSON><PERSON><PERSON>.Li
# @File    : create_prop.py
# @Desc    : DataHub 资产属性推送
"""

import json

import requests

from datahub_dev.config import api_token
from utils_new.pg_client import Database


def push_asset_properties(asset_urn, property_urn, values):
    """
    给DataHub资产推送结构化属性
    
    Args:
        asset_urn (str): 资产URN
        property_urn (str): 属性URN  
        values (list): 属性值列表，支持字符串和数字
    
    Returns:
        dict: API响应结果
    """

    # DataHub GraphQL API 端点
    url = "http://*************:9003/api/v2/graphql"

    # 请求头
    headers = {
        'Authorization': f'Bearer {api_token}',
        'Proxy-Connection': 'keep-alive',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'content-type': 'application/json'
    }

    # 构建属性值数组
    property_values = []
    for value in values:
        if isinstance(value, str):
            property_values.append({"stringValue": value})
        elif isinstance(value, (int, float)):
            property_values.append({"numberValue": value})

    # 构建请求数据
    payload = {
        "operationName": "upsertStructuredProperties",
        "variables": {
            "input": {
                "assetUrn": asset_urn,
                "structuredPropertyInputParams": [
                    {
                        "structuredPropertyUrn": property_urn,
                        "values": property_values
                    }
                ]
            }
        },
        "query": "mutation upsertStructuredProperties($input: UpsertStructuredPropertiesInput!) {\n  upsertStructuredProperties(input: $input) {\n    properties {\n      ...structuredPropertiesFields\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment structuredPropertiesFields on StructuredPropertiesEntry {\n  structuredProperty {\n    exists\n    ...structuredPropertyFields\n    __typename\n  }\n  values {\n    ... on StringValue {\n      stringValue\n      __typename\n    }\n    ... on NumberValue {\n      numberValue\n      __typename\n    }\n    __typename\n  }\n  valueEntities {\n    urn\n    type\n    ...entityDisplayNameFields\n    __typename\n  }\n  associatedUrn\n  __typename\n}\n\nfragment structuredPropertyFields on StructuredPropertyEntity {\n  urn\n  type\n  definition {\n    displayName\n    qualifiedName\n    description\n    cardinality\n    immutable\n    valueType {\n      urn\n      type\n      info {\n        type\n        displayName\n        __typename\n      }\n      __typename\n    }\n    entityTypes {\n      urn\n      type\n      info {\n        type\n        __typename\n      }\n      __typename\n    }\n    cardinality\n    typeQualifier {\n      allowedTypes {\n        urn\n        type\n        info {\n          type\n          displayName\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    allowedValues {\n      value {\n        ... on StringValue {\n          stringValue\n          __typename\n        }\n        ... on NumberValue {\n          numberValue\n          __typename\n        }\n        __typename\n      }\n      description\n      __typename\n    }\n    created {\n      time\n      actor {\n        urn\n        editableProperties {\n          displayName\n          pictureLink\n          __typename\n        }\n        ...entityDisplayNameFields\n        __typename\n      }\n      __typename\n    }\n    lastModified {\n      time\n      actor {\n        urn\n        editableProperties {\n          displayName\n          pictureLink\n          __typename\n        }\n        ...entityDisplayNameFields\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  settings {\n    isHidden\n    showInSearchFilters\n    showAsAssetBadge\n    showInAssetSummary\n    showInColumnsTable\n    __typename\n  }\n  __typename\n}\n\nfragment entityDisplayNameFields on Entity {\n  urn\n  type\n  ... on Dataset {\n    name\n    properties {\n      name\n      qualifiedName\n      __typename\n    }\n    __typename\n  }\n  ... on CorpUser {\n    username\n    properties {\n      displayName\n      title\n      firstName\n      lastName\n      fullName\n      email\n      __typename\n    }\n    info {\n      active\n      displayName\n      title\n      firstName\n      lastName\n      fullName\n      email\n      __typename\n    }\n    __typename\n  }\n  ... on CorpGroup {\n    name\n    info {\n      displayName\n      __typename\n    }\n    __typename\n  }\n  ... on Dashboard {\n    dashboardId\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on Chart {\n    chartId\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on DataFlow {\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on DataJob {\n    jobId\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on GlossaryTerm {\n    name\n    hierarchicalName\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on GlossaryNode {\n    properties {\n      name\n      description\n      __typename\n    }\n    __typename\n  }\n  ... on Domain {\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on Container {\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on MLFeatureTable {\n    name\n    __typename\n  }\n  ... on MLFeature {\n    name\n    __typename\n  }\n  ... on MLPrimaryKey {\n    name\n    __typename\n  }\n  ... on MLModel {\n    name\n    __typename\n  }\n  ... on MLModelGroup {\n    name\n    __typename\n  }\n  ... on Tag {\n    name\n    properties {\n      name\n      colorHex\n      __typename\n    }\n    __typename\n  }\n  ... on DataPlatform {\n    ...nonConflictingPlatformFields\n    __typename\n  }\n  ... on DataProduct {\n    properties {\n      name\n      __typename\n    }\n    __typename\n  }\n  ... on DataPlatformInstance {\n    instanceId\n    __typename\n  }\n  ... on StructuredPropertyEntity {\n    definition {\n      displayName\n      qualifiedName\n      __typename\n    }\n    __typename\n  }\n  ... on SchemaFieldEntity {\n    fieldPath\n    __typename\n  }\n  ... on OwnershipTypeEntity {\n    info {\n      name\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment nonConflictingPlatformFields on DataPlatform {\n  urn\n  type\n  name\n  properties {\n    displayName\n    datasetNameDelimiter\n    logoUrl\n    __typename\n  }\n  displayName\n  info {\n    type\n    displayName\n    datasetNameDelimiter\n    logoUrl\n    __typename\n  }\n  __typename\n}\n"
    }

    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()

        result = response.json()
        print(f"属性推送成功: {asset_urn}")
        return result

    except requests.exceptions.RequestException as e:
        print(f"属性推送失败: {e}")
        return None


def push_multiple_properties(asset_urn, properties_data):
    """
    给单个资产推送多个结构化属性
    
    Args:
        asset_urn (str): 资产URN
        properties_data (list): 属性数据列表，格式: [{"urn": "属性URN", "values": [值列表]}]
    
    Returns:
        dict: API响应结果
    """

    url = "http://*************:9003/api/v2/graphql"

    headers = {
        'Authorization': f'Bearer {api_token}',
        'Proxy-Connection': 'keep-alive',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'content-type': 'application/json'
    }

    # 构建多个属性参数
    structured_property_params = []
    for prop_data in properties_data:
        property_values = []
        for value in prop_data["values"]:
            if isinstance(value, str):
                property_values.append({"stringValue": value})
            elif isinstance(value, (int, float)):
                property_values.append({"numberValue": value})

        structured_property_params.append({
            "structuredPropertyUrn": prop_data["urn"],
            "values": property_values
        })

    payload = {
        "operationName": "upsertStructuredProperties",
        "variables": {
            "input": {
                "assetUrn": asset_urn,
                "structuredPropertyInputParams": structured_property_params
            }
        }
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()

        result = response.json()
        print(f"多属性推送成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
        return result

    except requests.exceptions.RequestException as e:
        print(f"多属性推送失败: {e}")
        return None


def main():
    """
    示例使用
    """
    # 示例1: 推送单个属性
    asset_urn = "urn:li:dataset:(urn:li:dataPlatform:postgres,fine_data.ads.ads_oprn_salesman_bamount_k_df,PROD)"
    property_urn = "urn:li:structuredProperty:cfb8c5df-285c-4da8-b826-86d4d3e45f0f"
    values = ["核心"]

    print("=== 推送单个属性 ===")
    push_asset_properties(asset_urn, property_urn, values)

    # # 示例2: 推送多个属性
    # properties_data = [
    #     {
    #         "urn": "urn:li:structuredProperty:cfb8c5df-285c-4da8-b826-86d4d3e45f0f",
    #         "values": ["重要", "核心"]
    #     },
    #     {
    #         "urn": "urn:li:structuredProperty:another-property-urn",
    #         "values": ["高质量", 95.5]
    #     }
    # ]
    #
    # print("\n=== 推送多个属性 ===")
    # push_multiple_properties(asset_urn, properties_data)


if __name__ == "__main__":
    db = Database()
    # 查询FDL任务之间的关系
    results = db.execute_sql("""
                             SELECT CONCAT('fine_data.', table_schema, '.', table_name) AS urn
                                  , table_level
                             FROM root_table_level
                             """)

    if results:
        for row in results:
            urn = row[0] if row[0] else "NULL"
            table_level = row[1] if row[1] else "NULL"
            property_urn = "urn:li:structuredProperty:e8f3ed96-19ec-417e-a94e-d5062faabec1"
            values = [table_level]
            push_asset_properties(f"urn:li:dataset:(urn:li:dataPlatform:postgres,{urn},PROD)", property_urn, values)
    else:
        print("未查询到任何数据")
