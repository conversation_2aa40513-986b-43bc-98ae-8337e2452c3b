/* 全局样式 - 升级为高端版本 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: #ffffff;
    color: #1a202c;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* 高端脉冲动画 */
@keyframes pulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 主容器升级 */
.main-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    backdrop-filter: blur(1px);
}

/* 高端顶部导航 */
.header {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(255, 255, 255, 0.95) 50%, 
        rgba(255, 255, 255, 0.92) 100%);
    backdrop-filter: blur(20px) saturate(1.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1.2rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header:hover {
    box-shadow: 
        0 12px 48px rgba(0, 0, 0, 0.15),
        0 4px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1.5rem;
}

/* Logo升级 */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo:hover {
    transform: translateY(-2px);
}

.logo i {
    font-size: 2.2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoGlow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.3));
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

@keyframes logoGlow {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.05) rotate(5deg); }
}

/* 高级搜索框 */
.search-container {
    position: relative;
    flex: 1;
    max-width: 500px;
}

#search-input {
    width: 100%;
    padding: 1rem 3.5rem 1rem 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 2px solid transparent;
    border-radius: 50px;
    color: #2d3748;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.06),
        0 4px 16px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
}

#search-input:focus {
    outline: none;
    border: 2px solid #667eea;
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 8px 24px rgba(102, 126, 234, 0.15),
        inset 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

#search-input::placeholder {
    color: rgba(107, 114, 126, 0.7);
    font-weight: 400;
}

.search-container i {
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-container:hover i {
    color: #764ba2;
    transform: translateY(-50%) scale(1.1);
}

/* 高级统计信息 */
.header-stats {
    display: flex;
    gap: 2.5rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.stat-item:hover {
    transform: translateY(-2px) scale(1.02);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-number {
    display: block;
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: countUp 2.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-label {
    font-size: 0.85rem;
    color: rgba(45, 55, 72, 0.8);
    font-weight: 600;
    margin-top: 0.2rem;
}

@keyframes countUp {
    from { 
        opacity: 0; 
        transform: translateY(20px) scale(0.8); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0) scale(1); 
    }
}

/* 主要内容升级 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem;
    position: relative;
}

/* 高端section标题 */
.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.75rem;
    margin-bottom: 2rem;
    color: #1a202c;
    font-weight: 700;
    position: relative;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: slideIn 0.6s ease-out;
}

.section-title i {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.3));
}

@keyframes slideIn {
    from { width: 0; }
    to { width: 60px; }
}

/* 数据平台概览升级 */
.platforms-overview {
    margin-bottom: 4rem;
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.platform-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
}

.platform-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--platform-color, #667eea) 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.platform-item:hover::before {
    transform: scaleX(1);
}

.platform-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 48px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
}

.platform-item.active {
    background: linear-gradient(135deg, var(--platform-color, #667eea) 0%, #764ba2 100%);
    color: white;
    transform: translateY(-4px) scale(1.02);
    box-shadow: 
        0 16px 40px rgba(0, 0, 0, 0.2),
        0 8px 16px var(--platform-color, rgba(102, 126, 234, 0.3));
}

.platform-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--platform-color, #667eea);
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.platform-item:hover .platform-icon {
    transform: scale(1.1) rotate(5deg);
}

.platform-item.active .platform-icon {
    color: white;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.platform-name {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.platform-item.active .platform-name {
    color: white;
}

.platform-count {
    font-size: 1.4rem;
    font-weight: 800;
    color: var(--platform-color, #667eea);
    margin-bottom: 0.3rem;
}

.platform-item.active .platform-count {
    color: white;
}

.platform-type {
    font-size: 0.9rem;
    color: rgba(45, 55, 72, 0.7);
    font-weight: 500;
}

.platform-item.active .platform-type {
    color: rgba(255, 255, 255, 0.9);
}

/* 数据资产分类升级 */
.asset-categories {
    margin-bottom: 4rem;
}

.categories-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.category-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 1px 4px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--category-color, #667eea) 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.category-item:hover::before {
    opacity: 0.05;
}

.category-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 
        0 12px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08);
}

.category-item.active {
    background: linear-gradient(135deg, var(--category-color, #667eea) 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 12px 32px rgba(0, 0, 0, 0.15),
        0 4px 16px var(--category-color, rgba(102, 126, 234, 0.3));
}

.category-icon {
    font-size: 2.5rem;
    color: var(--category-color, #667eea);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    display: block;
}

.category-item:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
}

.category-item.active .category-icon {
    color: white;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.category-name {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.category-item.active .category-name {
    color: white;
}

.category-count {
    font-size: 1.2rem;
    font-weight: 800;
    color: var(--category-color, #667eea);
}

.category-item.active .category-count {
    color: white;
}

/* 域卡片网格升级 */
.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 高端域卡片 */
.domain-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 24px;
    padding: 2.5rem;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.domain-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--domain-color, #667eea) 0%, #764ba2 50%, #f093fb 100%);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.domain-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, var(--domain-color, #667eea)10, transparent 50%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.domain-card:hover::before {
    transform: scaleX(1);
}

.domain-card:hover::after {
    opacity: 0.03;
}

.domain-card:hover {
    transform: translateY(-12px) scale(1.02) rotateX(2deg);
    box-shadow: 
        0 25px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.6);
}

/* 高端域卡片内部元素 */
.domain-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.domain-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1a202c;
    letter-spacing: -0.2px;
}

.domain-icon {
    font-size: 2.5rem;
    color: var(--domain-color, #667eea);
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.domain-card:hover .domain-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

.domain-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.domain-stat {
    text-align: center;
    background: #ffffff;
    padding: 16px 12px;
    border-radius: 12px;
    border: 1px solid #e5e5e7;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.domain-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-color: #d2d2d7;
}

.domain-stat-number {
    display: block;
    font-size: 24px;
    line-height: 1.16667;
    font-weight: 600;
    letter-spacing: 0.009em;
    color: #007aff;
}

.domain-stat-label {
    font-size: 14px;
    line-height: 1.42857;
    color: #86868b;
    margin-top: 4px;
    font-weight: 400;
}

.domain-asset-types {
    margin-top: 24px;
}

.asset-type-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.asset-type-tag {
    background: #ffffff;
    color: #007aff;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    line-height: 1.42857;
    font-weight: 400;
    border: 1px solid #e5e5e7;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.asset-type-tag:hover {
    background: #007aff;
    color: white;
    border-color: #007aff;
}

.domain-description {
    color: #86868b;
    font-size: 17px;
    line-height: 1.47059;
    font-weight: 400;
    letter-spacing: -0.022em;
}

/* 数据血缘关系图 - 直接连接设计 */
.data-flow-section {
    margin-bottom: 60px;
}

.lineage-container {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    position: relative;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.08),
        0 4px 8px rgba(0, 0, 0, 0.04);
}

/* 血缘关系图头部 */
.lineage-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lineage-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 2px 0;
}

.lineage-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.view-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

/* 血缘关系图主容器 */
.lineage-diagram {
    position: relative;
    width: 100%;
    height: 400px;
    padding: 20px;
    overflow: hidden;
}

/* SVG连接线容器 */
#lineage-svg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
}

#lineage-svg * {
    pointer-events: auto;
}

/* 节点容器 */
.lineage-nodes {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* 直接连接节点样式 */
.lineage-node-direct {
    position: absolute;
    width: 110px;
    height: 60px;
    background: white;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.lineage-node-direct:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #4a90e2;
}

.lineage-node-direct.node-active {
    border-color: #ff6b6b;
    box-shadow: 
        0 8px 16px rgba(255, 107, 107, 0.2),
        0 0 0 2px rgba(255, 107, 107, 0.1);
}

.lineage-node-direct.node-connected {
    opacity: 1 !important;
    filter: saturate(1) !important;
    transform: scale(1.03);
    border-color: #4a90e2;
}

.lineage-node-direct.node-dimmed {
    opacity: 0.4;
    filter: saturate(0);
}

/* 节点内容 */
.node-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 8px;
}

.node-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
}

.node-title h4 {
    font-size: 0.75rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90px;
}

/* Elegant Lineage Diagram Styling */

.connection-group {
    transition: opacity 0.3s ease;
    z-index: 1;
}

.connection-group.dimmed {
    opacity: 0.15;
}

.connection-group.active {
    z-index: 10;
    opacity: 1 !important;
}

.connection-line {
    fill: none;
    stroke: #a0c3ff;
    stroke-width: 1.5;
    stroke-linecap: round;
    transition: all 0.3s ease;
}

.connection-group.active .connection-line {
    stroke: #4a90e2;
    stroke-width: 2.5;
}

.arrow-path {
    fill: #a0c3ff;
    transition: fill 0.3s ease;
}
.connection-group.active .arrow-path {
    fill: #4a90e2;
}

/* Capsule-style labels */
.line-label-capsule {
    display: inline-block;
    padding: 3px 10px;
    background-color: #f0f5ff;
    border: 1px solid #d6e4ff;
    color: #3e5b8a;
    font-size: 10px;
    font-weight: 500;
    border-radius: 12px;
    font-family: 'Inter', sans-serif;
    text-align: center;
    white-space: nowrap;
    transition: all 0.3s ease;
    transform: scale(1);
    pointer-events: none;
}

.connection-group.active .line-label-capsule {
    background-color: #4a90e2;
    border-color: #4a90e2;
    color: #ffffff;
    font-weight: 600;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

/* 电流动画效果 */
@keyframes electricFlow {
    0% {
        stroke-dashoffset: 0;
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    90% {
        opacity: 0.8;
    }
    100% {
        stroke-dashoffset: 16;
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 节点入场动画 */
.lineage-node-direct {
    animation: nodeSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

@keyframes nodeSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 连接线入场动画 */
.connection-group {
    animation: connectionFadeIn 0.8s ease-out both;
}

@keyframes connectionFadeIn {
    from {
        opacity: 0;
        stroke-dasharray: 0, 1000;
    }
    to {
        opacity: 1;
        stroke-dasharray: none;
    }
}

/* 发光效果 */
.lineage-node-direct.node-active .node-icon {
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.15),
        0 0 12px rgba(74, 144, 226, 0.4);
}

/* 紧凑视图 */
.lineage-diagram.compact-view {
    height: 300px;
}

.lineage-diagram.compact-view .lineage-node-direct {
    width: 90px;
    height: 50px;
}

.lineage-diagram.compact-view .node-icon {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
}

.lineage-diagram.compact-view .node-title h4 {
    font-size: 0.7rem;
    max-width: 75px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .lineage-diagram {
        transform: scale(0.85);
        transform-origin: center top;
        height: 340px;
    }
}

@media (max-width: 768px) {
    .lineage-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 16px 20px;
    }
    
    .lineage-diagram {
        transform: scale(0.7);
        transform-origin: center top;
        height: 280px;
        padding: 10px;
    }
    
    .lineage-node-direct {
        width: 80px;
        height: 45px;
    }
    
    .node-icon {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }
    
    .node-title h4 {
        font-size: 0.65rem;
        max-width: 65px;
    }
}

@media (max-width: 480px) {
    .lineage-diagram {
        transform: scale(0.6);
        height: 240px;
    }
}

/* 工具提示 */
.connection-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.connection-tooltip.show {
    opacity: 1;
}

/* 节点详情模态框样式保持不变 */
.node-detail-modal {
    max-width: 500px;
}

.node-detail-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.node-detail-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.node-detail-title h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.node-detail-title p {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 10px 0;
}

.connection-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.connection-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 10px;
    background: #f9fafb;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.connection-type {
    font-size: 0.7rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    min-width: 50px;
}

.connection-label {
    font-size: 0.8rem;
    color: #374151;
    font-weight: 500;
}

.status-indicators {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: #374151;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #10b981;
}

.status-dot.active {
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.no-connections {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    padding: 12px;
    font-size: 0.8rem;
}

/* 高端热门数据资产 */
.assets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 4rem;
}

/* 高端资产卡片 */
.asset-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 20px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.asset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.03) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.asset-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.asset-card:hover::before {
    opacity: 1;
}

.asset-card:hover::after {
    transform: scaleX(1);
}

.asset-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 48px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 高端资产卡片内部元素 */
.asset-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.2rem;
}

.asset-icon {
    font-size: 2rem;
    color: #667eea;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.asset-card:hover .asset-icon {
    transform: scale(1.1) rotate(5deg);
    color: #764ba2;
}

.asset-type {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    color: #667eea;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: auto;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.asset-card:hover .asset-type {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.asset-name {
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    letter-spacing: -0.2px;
}

.asset-platform {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
    color: #667eea;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%);
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    width: fit-content;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s ease;
}

.asset-platform:hover {
    transform: translateY(-1px);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.asset-platform i {
    font-size: 0.9rem;
}

.asset-description {
    color: rgba(26, 32, 44, 0.7);
    font-size: 0.95rem;
    margin-bottom: 1.2rem;
    line-height: 1.5;
    font-weight: 400;
}

.asset-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: rgba(26, 32, 44, 0.6);
}

.asset-domain {
    color: #667eea;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.4) 100%);
    padding: 0.3rem 0.6rem;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.asset-usage {
    padding: 0.3rem 0.7rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.asset-usage:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.asset-usage.high {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 53, 69, 0.1) 100%);
    color: #dc3545;
}

.asset-usage.medium {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
    color: #f59e0b;
}

.asset-usage.low {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(25, 135, 84, 0.1) 100%);
    color: #22c55e;
}

/* 高端数据质量监控 */
.quality-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

/* 高端质量卡片 */
.quality-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 24px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quality-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.quality-card:hover::before {
    opacity: 1;
}

.quality-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 高端质量卡片内部元素 */
.quality-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.quality-card:hover .quality-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.3));
}

.quality-info h3 {
    margin-bottom: 0.75rem;
    color: #1a202c;
    font-size: 1.1rem;
    font-weight: 700;
    letter-spacing: -0.2px;
}

.progress-bar {
    width: 220px;
    height: 12px;
    background: linear-gradient(135deg, rgba(243, 244, 246, 0.8) 0%, rgba(229, 231, 235, 0.6) 100%);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 0.75rem;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 12px;
    width: 0;
    animation: progressAnimation 2.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressAnimation {
    to { width: var(--progress); }
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-weight: 800;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #ffffff;
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 15px;
    width: 80%;
    max-width: 800px;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #495057;
}

/* 高端加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner p {
    color: #1a202c;
    font-size: 1.1rem;
    margin-top: 1.5rem;
    font-weight: 500;
    animation: fadeInOut 2s ease-in-out infinite;
}

.spinner {
    width: 80px;
    height: 80px;
    position: relative;
    margin: 0 auto;
}

.spinner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border: 4px solid transparent;
    border-bottom: 4px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: spin 1s linear infinite reverse;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* 额外动画效果 */
.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(64, 224, 255, 0.2);
    }
    to {
        box-shadow: 0 0 20px rgba(64, 224, 255, 0.6), 0 0 30px rgba(64, 224, 255, 0.4);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据流动画增强 */
.flow-particle {
    fill: #40e0ff;
    r: 3;
    animation: flowParticle 4s infinite linear;
}

@keyframes flowParticle {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

/* 悬停效果增强 */
.domain-card:hover .domain-icon {
    animation: iconSpin 0.5s ease-in-out;
}

@keyframes iconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.table-card:hover {
    background: linear-gradient(135deg, rgba(64, 224, 255, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
}

/* 搜索建议框 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 10px;
    margin-top: 5px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid rgba(108, 117, 125, 0.1);
    color: #212529;
}

.suggestion-item:hover {
    background: rgba(248, 249, 250, 0.8);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* 模态框内容样式 */
.domain-detail-stats {
    display: flex;
    justify-content: space-around;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.domain-detail-stats .stat-item {
    text-align: center;
}

.domain-detail-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.domain-detail-stats .stat-label {
    font-size: 0.9rem;
    color: rgba(108, 117, 125, 0.8);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(233, 236, 239, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #495057, #6c757d);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #6c757d, #495057);
}

/* 资产详情样式 */
.asset-detail-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.asset-type-badge, .asset-domain-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-type-badge {
    background: rgba(248, 249, 250, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-domain-badge {
    background: rgba(233, 236, 239, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-platform-badge {
    background: rgba(220, 248, 198, 0.8);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.3);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.usage-high {
    color: #dc3545 !important;
}

.usage-medium {
    color: #ffc107 !important;
}

.usage-low {
    color: #198754 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-stats {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .platforms-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .categories-nav {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .main-content {
        padding: 1rem;
    }

    .domains-grid {
        grid-template-columns: 1fr;
    }

    .assets-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }

    .quality-dashboard {
        grid-template-columns: 1fr;
    }

    .domain-detail-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .asset-detail-stats {
        grid-template-columns: 1fr;
    }

    #lineage-svg {
        height: 400px;
    }
}
